# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: file_transfer.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import builder as _builder
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x13\x66ile_transfer.proto\"]\n\x0b\x46ileRequest\x12\x14\n\x0c\x66ile_content\x18\x01 \x01(\x0c\x12\x11\n\tfile_name\x18\x02 \x01(\t\x12\x11\n\tclient_ip\x18\x03 \x01(\t\x12\x12\n\nstudent_id\x18\x04 \x01(\t\"\x1f\n\x0c\x46ileResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x32\x44\n\x13\x46ileTransferService\x12-\n\x0cTransferFile\x12\x0c.FileRequest\x1a\r.FileResponse\"\x00\x62\x06proto3')

_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, globals())
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'file_transfer_pb2', globals())
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  _FILEREQUEST._serialized_start=23
  _FILEREQUEST._serialized_end=116
  _FILERESPONSE._serialized_start=118
  _FILERESPONSE._serialized_end=149
  _FILETRANSFERSERVICE._serialized_start=151
  _FILETRANSFERSERVICE._serialized_end=219
# @@protoc_insertion_point(module_scope)
