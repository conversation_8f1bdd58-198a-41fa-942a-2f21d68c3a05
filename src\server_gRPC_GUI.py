import grpc  
from grpc_api import login_service_pb2  
from grpc_api import login_service_pb2_grpc
from grpc_api import file_transfer_pb2
from grpc_api import file_transfer_pb2_grpc


import sqlite3 
import os
import time
from datetime import datetime  
from concurrent import futures
import sys
from PySide2.QtWidgets import QApplication, QMainWindow, QLabel, QTableWidget, QTableWidgetItem, QComboBox, QVBoxLayout, QWidget,QPushButton,QTabWidget
from PySide2.QtWidgets import QMessageBox
from PySide2.QtCore import Qt, QThread, Signal, Slot
from PySide2 import QtWidgets
import json
import logging

# 读入config.json文件  
with open('config.json', 'r') as f:  
    config = json.load(f)  
  
# 提取服务器地址和端口参数  
server_ip = config.get('server_ip')  
server_port = config.get('server_port')
SEND_MAX_SIZE = int(config.get('send_max_size'))
RECEIVE_MAX_SIZE = int(config.get('receive_max_size'))

# 配置日志记录  
logging.basicConfig(filename='log.txt', level=logging.DEBUG, format='%(asctime)s: %(message)s')


Login_dict = dict()
# Login_dict['1233'] = ['1231111111','123111110']




class FileTransferService(file_transfer_pb2_grpc.FileTransferServiceServicer):  
    def TransferFile(self, request, context):
        # 获取当前时间戳，并将其转换为“年月日时分秒毫秒”的格式  
        timestamp = int(time.time())  
        
        
        client_ip = request.client_ip.replace('.', '_')
        student_id = request.student_id
        dir_name = os.path.join('.', student_id)  
        if not os.path.exists(dir_name):  
            os.makedirs(dir_name)  
        file_path = os.path.join(dir_name, request.file_name)
        if request.file_name == "log.txt":
            Login_dict[student_id].append(timestamp)
            
          
        with open(file_path, 'wb') as f:  
            f.write(request.file_content)  
          
        return file_transfer_pb2.FileResponse(success=True)



class LoginServiceApp(QMainWindow):
    def __init__(self):
        super(LoginServiceApp, self).__init__()
        self.server_thread = None  # 用于存储服务线程
        self.initUI()
        
    def closeEvent(self, event):        
        # 创建一个消息框，询问用户是否真的要关闭  
        reply = QMessageBox.question(self, '关闭对话框',  
            '你确定要关闭吗?', QMessageBox.Yes |   
            QMessageBox.No, QMessageBox.No)  
  
        # 如果用户点击了“是”，则关闭对话框  
        if reply == QMessageBox.Yes:  
            event.accept()  
        else:  
            # 否则，忽略关闭事件，对话框保持打开状态  
            event.ignore()  
        

    def initUI(self):
        querysql='''  
            SELECT StudentInfo.id, StudentInfo.name, ClassRecord.client_ip, ClassRecord.hostname, ClassRecord.timestamp  
            FROM StudentInfo  
            LEFT JOIN ClassRecord ON StudentInfo.id = ClassRecord.student_id 
             '''
        # 主窗口布局
        self.mainWidget = QWidget(self)
        self.setCentralWidget(self.mainWidget)
        self.mainLayout = QVBoxLayout(self.mainWidget)
        
        # 创建标签页控件  
        self.tab_widget = QTabWidget()  
  
        # 创建状态标签
        self.statusLabel = QLabel("服务状态：未运行", self)
        self.mainLayout.addWidget(self.statusLabel)
        # 添加启动和停止按钮
        self.startButton = QPushButton('启动服务', self)
        self.stopButton = QPushButton('停止服务', self)
        self.mainLayout.addWidget(self.startButton)
        self.mainLayout.addWidget(self.stopButton)

        # 连接按钮和相应的方法
        self.startButton.clicked.connect(self.start_server)
        self.stopButton.clicked.connect(self.stop_server)
        
        # 创建第一个标签页和表格  
        tab1 = QWidget()  
        tab1_layout = QVBoxLayout()  

        # 创建表格
        self.tableWidget = QTableWidget(200,5)
        #self.tableWidget.setColumnCount(3)  # 假设有3列，例如：姓名、ID和登录状态      
        self.tableWidget.setHorizontalHeaderLabels(["姓名", "学号", "登录状态(IP)","客户机名称","登录时间"])
        # 更新表格数据        
        
        data = self.query_data(querysql)
        for row, (id, name, client_ip, client_name, timestamp) in enumerate(data):
            self.tableWidget.setItem(row, 0, QtWidgets.QTableWidgetItem(name))
            self.tableWidget.setItem(row, 1, QtWidgets.QTableWidgetItem(str(id)))            
            self.tableWidget.setItem(row, 2, QtWidgets.QTableWidgetItem(client_ip))
            self.tableWidget.setItem(row, 3, QtWidgets.QTableWidgetItem(client_name))
            self.tableWidget.setItem(row, 4, QtWidgets.QTableWidgetItem(timestamp))
        
        self.tableWidget.sortItems(2, Qt.AscendingOrder)
        
        tab1_layout.addWidget(self.tableWidget)  
        tab1.setLayout(tab1_layout)  
        self.tab_widget.addTab(tab1, "学生名单")
        
        
        
        
        # 创建第二个标签页和表格  
        tab2 = QWidget()  
        tab2_layout = QVBoxLayout()  
        self.table2 = QTableWidget(300,3)
        self.table2.setHorizontalHeaderLabels(["姓名", "学号", "登录状态"])
        self.update_linkstatus()
        tab2_layout.addWidget(self.table2)  
        tab2.setLayout(tab2_layout)
        
        self.tab_widget.addTab(tab2, "学生机状态")  
  
        self.mainLayout.addWidget(self.tab_widget)
        

        # 创建下拉框
        self.comboBox = QComboBox(self)
        self.comboBox.addItem("所有学生")
        self.comboBox.addItem("已登录")
        self.comboBox.addItem("未登录")
        self.mainLayout.addWidget(self.comboBox)

        # 连接信号和槽
        self.comboBox.currentIndexChanged.connect(self.on_combobox_changed)

        # 设置窗口属性
        self.setWindowTitle('教师机服务程序')
        self.show()

    @Slot()
    def on_combobox_changed(self, index):
        
        self.update_linkstatus()

        #print(index)
        # 处理下拉框变化
        if index == 0:
            querysql='''  
            SELECT StudentInfo.id, StudentInfo.name, ClassRecord.client_ip, ClassRecord.hostname, ClassRecord.timestamp  
            FROM StudentInfo  
            LEFT JOIN ClassRecord ON StudentInfo.id = ClassRecord.student_id 
             '''
        elif index == 1:
            querysql='''  
            SELECT StudentInfo.id, StudentInfo.name, ClassRecord.client_ip, ClassRecord.hostname, ClassRecord.timestamp  
            FROM StudentInfo  
            JOIN ClassRecord ON StudentInfo.id = ClassRecord.student_id 
            '''
        else:
            querysql='''  
            SELECT StudentInfo.id, StudentInfo.name,ClassRecord.client_ip, ClassRecord.hostname, ClassRecord.timestamp 
            FROM StudentInfo  
            LEFT JOIN ClassRecord ON StudentInfo.id = ClassRecord.student_id  
            WHERE ClassRecord.student_id IS NULL'''
        self.update_table(querysql)        
        

    def update_table(self,querysql):
        # 更新表格数据
        data = self.query_data(querysql)
        self.tableWidget.clearContents()
        for row, (id, name, client_ip, client_name, timestamp) in enumerate(data):
            self.tableWidget.setItem(row, 0, QtWidgets.QTableWidgetItem(name))
            self.tableWidget.setItem(row, 1, QtWidgets.QTableWidgetItem(str(id)))            
            self.tableWidget.setItem(row, 2, QtWidgets.QTableWidgetItem(client_ip))
            self.tableWidget.setItem(row, 3, QtWidgets.QTableWidgetItem(client_name))
            self.tableWidget.setItem(row, 4, QtWidgets.QTableWidgetItem(timestamp))
    
    def update_linkstatus(self):
        row = 0
        for key in Login_dict:
            #print(key)
            #print(Login_dict[key])
            self.table2.setItem(row, 0, QtWidgets.QTableWidgetItem(Login_dict[key][0]))        
            self.table2.setItem(row, 1, QtWidgets.QTableWidgetItem(key))
            if link_timeout(key):
                self.table2.setItem(row, 2, QtWidgets.QTableWidgetItem("正常"))
            else:
                self.table2.setItem(row, 2, QtWidgets.QTableWidgetItem("离线"))
                logging.info(f"学号{key}姓名{Login_dict[key][0]}离线 ")
                
            row+=1
            
               
#             self.table.setItem(row, 2, QtWidgets.QTableWidgetItem(client_ip))  
#             self.table.setItem(row, 3, QtWidgets.QTableWidgetItem(hostname))  
#             self.table.setItem(row, 4, QtWidgets.QTableWidgetItem(timestamp))
        

    def update_status(self, status):
        # 更新服务状态
         self.statusLabel.setText(f"服务状态：{status}")         
        
    def query_data(self,query):
        
        # 获取当前日期的时间戳字符串（格式为"年月日时分秒毫秒"）  
        # now = time.strftime("%Y%m%d%H%M%S%f", time.localtime())
        
        conn = sqlite3.connect("netclass.db")  
        cursor = conn.cursor()  
  
        # 查询StudentInfo和ClassRecord两表，通过学生号连接，并筛选时间戳为今天的数据  
#         self.cursor.execute('''  
#             SELECT StudentInfo.id, StudentInfo.name, ClassRecord.client_ip, ClassRecord.hostname, ClassRecord.timestamp  
#             FROM StudentInfo  
#             JOIN ClassRecord ON StudentInfo.id = ClassRecord.student_id  
#             WHERE ClassRecord.timestamp LIKE ?  
#         ''', (now,))

#         cursor.execute('''  
#             SELECT StudentInfo.id, StudentInfo.name, ClassRecord.client_ip, ClassRecord.hostname, ClassRecord.timestamp  
#             FROM StudentInfo  
#             JOIN ClassRecord ON StudentInfo.id = ClassRecord.student_id 
#              ''')
        cursor.execute(query)
        # 获取查询结果  
        results = cursor.fetchall()  
  
        # 关闭数据库连接  
        conn.close()  
  
        return results  
    
    def start_server(self):
        if self.server_thread is None:
            self.server_thread = ServerThread()  # 创建服务线程
            self.server_thread.start()  # 启动线程
            self.update_status("服务运行中")

    def stop_server(self):
          # 创建一个消息框，询问用户是否真的要关闭  
        reply = QMessageBox.question(self, '关闭对话框',  
            '你确定要关闭吗?', QMessageBox.Yes |   
            QMessageBox.No, QMessageBox.No)  
  
        # 如果用户点击了“是”，则关闭对话框  
        if reply == QMessageBox.Yes:  
            if self.server_thread is not None:
                self.server_thread.stop()  # 停止线程
                self.server_thread = None
                self.update_status("服务已停止")        
        


# 服务线程类
class ServerThread(QThread):
    def __init__(self):
        super(ServerThread, self).__init__()
        self.server = None

    def run(self):
        self.server = grpc.server(futures.ThreadPoolExecutor(max_workers=400),options=[('grpc.max_send_message_length',
                                                      SEND_MAX_SIZE),('grpc.max_receive_message_length',
                                                      RECEIVE_MAX_SIZE)])
        login_service_pb2_grpc.add_LoginServiceServicer_to_server(LoginServiceServicer(), self.server)
        file_transfer_pb2_grpc.add_FileTransferServiceServicer_to_server(FileTransferService(), self.server) 
        self.server.add_insecure_port(f'[::]:{server_port}')
        
        self.server.start()

    def stop(self):
        if self.server is not None:
            self.server.stop(0)  # 停止GRPC服务


  
class LoginServiceServicer(login_service_pb2_grpc.LoginServiceServicer):  
    def __init__(self, db_name='netclass.db'):
        self.db_name = db_name
        
  
    def Login(self, request, context):
        self.conn = sqlite3.connect(self.db_name)  
        self.cursor = self.conn.cursor()  
        username = request.username  
        password = request.password  
        clientip = request.clientip  
        hostname = request.hostname  
        
        # 获取当前时间戳，并将其转换为“年月日时分秒毫秒”的格式  
        timestamp = int(time.time())  
        timestamp_str = datetime.fromtimestamp(timestamp).strftime('%Y%m%d%H%M%S%f')[:-3] 
        
          
        # 查询StudentInfo表，验证用户名和密码  
        self.cursor.execute("SELECT * FROM StudentInfo WHERE name=? AND id=?", (username, password))  
        result = self.cursor.fetchone()
        
          
        if result is not None:  
            # 将登录信息记录到ClassRecord表
            #timestamp_datetime = datetime.strptime(timestamp_str, '%Y%m%d%H%M%S%f')  
  
            #formatted_datetime = timestamp_datetime.strftime('%Y年%m月%d日%H时%M分%S秒')  
            
            self.cursor.execute("INSERT INTO ClassRecord(student_id, client_ip, hostname, timestamp) VALUES(?,?,?,?)",   
                                (password, clientip, hostname, timestamp_str))
            
            Login_dict[password] = [result[1],timestamp]
            
            
            self.conn.commit()  
              
            # 根据学生ID创建目录  
            dir_name = f"{password}"  
            if not os.path.exists(dir_name):  
                os.makedirs(dir_name)  
              
            return login_service_pb2.LoginResponse(success=True, message="登录成功")  
        else:  
            return login_service_pb2.LoginResponse(success=False, message="用户名或密码错误")  
  
# def serve():  
#     server = grpc.server(futures.ThreadPoolExecutor(max_workers=200))  
#     login_service_pb2_grpc.add_LoginServiceServicer_to_server(LoginServiceServicer(), server)  
#     server.add_insecure_port('[::]:50051')  
#     server.start()  
#     server.wait_for_termination()

def link_timeout(key):
    timestamp = int(time.time())  
    timeout = timestamp-int(Login_dict[key][-1])
    if timeout>15:
        return False
    else:
        return True


def main():
    app = QApplication(sys.argv)
    ex = LoginServiceApp()
    sys.exit(app.exec_())

  
if __name__ == '__main__':  
    main()