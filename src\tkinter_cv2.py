import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import cv2
from PIL import Image, ImageTk
import threading
import os
import socket
import grpc
import datetime
import time
import zipfile
import json
import psutil
import logging
import sys

import numpy as np
from grpc_api import login_service_pb2  
from grpc_api import login_service_pb2_grpc
from grpc_api import file_transfer_pb2
from grpc_api import file_transfer_pb2_grpc

#import grpc_api.login_service_pb2
#import grpc_api.login_service_pb2_grpc

#import login_service_pb2
#import login_service_pb2_grpc

# import file_transfer_pb2
# import file_transfer_pb2_grpc

import pyautogui


import logging

# 创建线程对象  
threads = []

# 主窗口
MAIN_WIN = None

# 配置日志记录  
logging.basicConfig(filename='log.txt', level=logging.DEBUG, format='%(asctime)s: %(message)s')

# 设置视频编码器
fourcc = cv2.VideoWriter_fourcc(*"mp4v")

# 读入config.json文件  
with open('config.json', 'r') as f:  
    config = json.load(f)  
  
# 提取服务器地址和端口参数  
server_ip = config.get('server_ip')  
server_port = config.get('server_port')
# 文件传输超时时间
file_trans_timeout = int(config.get('file_trans_timeout'))
# 文件传输失败后重试的次数
RE_MAX  = int(config.get('re_max'))
# 记录传输失败的文件队列
FAIL_FILES = dict()
# 定义保存视频的间隔时间（单位为秒）
video_save_interval = int(config.get('video_save_interval'))
# 截图间隔时间(单位：秒)
interval_time = int(config.get('interval_time'))
# 重传间隔时间
re_interval_time = int(config.get('re_interval_time'))
# windows 高
Height = int(config.get('height'))
# windows 宽
Width = int(config.get('width'))

SEND_MAX_SIZE = int(config.get('send_max_size'))
RECEIVE_MAX_SIZE = int(config.get('receive_max_size'))



student_no = '666'

hostname = socket.gethostname()
clientip = socket.gethostbyname(hostname)

program_run = True

# 获取屏幕尺寸
screen_width, screen_height = pyautogui.size()

# 设置视频编码器
out = None


# 获取当前时间
start_time = time.time()
current_time = time.time()
end_time = current_time + video_save_interval

def is_server_up(ip, port):
    """检查服务器是否可达"""
    try:
        # 使用socket尝试连接服务器
        with socket.create_connection((ip, port), timeout=3):
            return True
    except (socket.timeout, ConnectionRefusedError, OSError):
        return False


def login(username, password):
    try:
        print(f"{clientip}")
        with grpc.insecure_channel(f'{server_ip}:{server_port}') as channel:
#         channel = grpc.insecure_channel(f'{server_ip}:{server_port}')
            stub = login_service_pb2_grpc.LoginServiceStub(channel)
            request = login_service_pb2.LoginRequest(username=username, password=password,
                                                         clientip=clientip, hostname=hostname)
            if is_server_up(server_ip,server_port):
                response = stub.Login(request,timeout=3)
                return (response.success,response.message)
            else:
                return (False, "无法连接到服务器")               
            
        
#         return (False, "无法连接到服务器")
#     except grpc.aio.AioRpcError as e:  
#         print("Failed to connect to the server:", e)
    except grpc.RpcError as e:
        # 如果是连接失败的异常，打印消息并返回对应信息
        if e.code() == grpc.StatusCode.UNAVAILABLE:
            print("无法连接到服务器: 服务不可用")
        else:
            print(f"RPC调用失败: {e}")
        return (False, "无法连接到服务器")
    except BaseException as e:
         # 如果服务器连接失败，打印错误信息并返回  
        print(f"无法连接到服务器: {e}")  
        return (False, "无法连接到服务器")
    else:
        print("------")
    # return (False,"Not Success!")
# 示例：login('user', 'password')


def transfer_file(file_path, client_ip='localhost'):
    try:
        if is_server_up(server_ip,server_port):
            with open(file_path, 'rb') as f:  
                file_content = f.read()  
            global student_no
            channel = grpc.insecure_channel(f'{server_ip}:{server_port}',
                                            options=[('grpc.max_send_message_length',
                                                      SEND_MAX_SIZE),
                                                     ('grpc.max_receive_message_length',
                                                      RECEIVE_MAX_SIZE)])  
            stub = file_transfer_pb2_grpc.FileTransferServiceStub(channel)  
            response = stub.TransferFile(file_transfer_pb2.FileRequest(file_content=file_content,
                                                                       file_name=os.path.basename(file_path),
                                                                       client_ip=client_ip,
                                                                       student_id=student_no),timeout = file_trans_timeout
                                         )
            logging.info(f"文件{file_path}传输成功！")
            return response.success
        else:            
            logging.info(f"文件{file_path}传输失败！无法连接服务器。")
            if file_path in FAIL_FILES:
                FAIL_FILES[file_path] += 1
            else:
                FAIL_FILES[file_path] = 1
            return False
                
    except Exception as e:
        if file_path in FAIL_FILES:
                FAIL_FILES[file_path] += 1
        else:
                FAIL_FILES[file_path] = 1
        logging.info(e)
        return False
    
    
def send_log():
    global program_run
    Err = True
    while program_run:
        if not transfer_file('log.txt',clientip):
            # MAIN_WIN.textEdit.append("\n [重要信息]服务器连接错误！！！", MAIN_WIN.format)
            #MAIN_WIN.text_display.insert('end', "\n [重要信息]服务器连接错误！！！")
            MAIN_WIN.append_text("\n [连接错误]")
            Err = True
        elif Err==True:
            # MAIN_WIN.text_display.insert('end',"\n [重要信息]服务器连接成功！！！")
            MAIN_WIN.append_text("\n [连接成功]")
            Err = False
        else:
            Err = False
        for i in range(11):
            if not program_run:
                print(program_run)
                break
            time.sleep(1)
        

def retransfiles():
    global program_run
    while program_run:
        for video_filename in list(FAIL_FILES):
            if FAIL_FILES[video_filename]<=RE_MAX:                
                if transfer_file(video_filename,clientip):
                    del FAIL_FILES[video_filename]
                else:
                    FAIL_FILES[video_filename]+=1
            else:
                logging.info(f"文件{video_filename}超过了最大重传数。")
        for i in range(re_interval_time):
            if not program_run:
                break
            time.sleep(1)
        

def run():
    #channel = grpc.insecure_channel(f'{server_ip}:{server_port}')
    #stub = screen_pb2_grpc.ScreenCaptureStub(channel)
    global program_run
    while program_run:
        # 获取当前时间
        global current_time
        global end_time
        global out
        current_time = time.time()
         # 截取屏幕图像
        try:
            screenshot = pyautogui.screenshot()
            resized_screenshot = screenshot.resize((1280, 720), Image.LANCZOS)
        except Exception as e:
            print(f"Error: {e}")

        screenshot_np = np.array(resized_screenshot)
        screenshot_cv = cv2.cvtColor(screenshot_np, cv2.COLOR_RGB2BGR)
        
        # 如果当前时间超过保存视频的时间间隔，创建新的视频文件
        if current_time >= end_time:
            if out is not None:
                out.release()  # 关闭之前的视频文件                
                # 将视频文件发送到服务器
                transfer_file(video_filename,clientip)

            # 获取当前时间的时间戳，并将其作为视频文件名
            # 获取当前日期和时间
            now = datetime.datetime.now()
            # 格式化日期和时间
            formatted_now = now.strftime("%Y%m%d%H%M%S")

            # 将格式化后的日期和时间字符串作为视频文件名
            video_filename = formatted_now + ".mp4"
            # video_filename = f"{int(current_time)}.mp4"
            # out = cv2.VideoWriter(video_filename, fourcc, 2.0, (screen_width, screen_height))
            out = cv2.VideoWriter(video_filename, fourcc, 2, (1280, 720))
            # 更新下一个视频保存时间
            end_time = current_time + video_save_interval

        # 保存截图到视频
        if out is not None:
            out.write(screenshot_cv)

        
        
        # screenshot = pyautogui.screenshot()
        # screenshot_bytes = io.BytesIO()

        # screenshot.save(screenshot_bytes, format="PNG")
        
    #    response = stub.SendScreen(screen_pb2.ScreenRequest(image=screenshot_bytes.getvalue()))
     #   print(response.status)
        
        # 暂停2秒
        time.sleep(interval_time)
    else:
        if out is not None:
                out.release()  # 关闭之前的视频文件                
                # 将视频文件发送到服务器
                transfer_file(video_filename,clientip)


# class ConfirmExitDialog(tk.Toplevel):
#     def __init__(self, parent=None):
#         super().__init__(parent)
#         self.title('确认退出')
#         self.line_edit = ttk.Entry(self)
#         self.line_edit.pack()
#         ttk.Label(self, text="输入你的学号退出! 退出后会关闭计算机!!!").pack()
#         self.ok_button = ttk.Button(self, text='确定', command=self.on_ok_button_click)
#         self.ok_button.pack()
# 
#     def on_ok_button_click(self):
#         if self.line_edit.get().lower() == student_no:
#             global program_run
#             program_run = False
#             for t in threads:
#                 t.join()
#             os.system('shutdown /s /t 0')  # Suitable for Windows systems
#             self.destroy()
#         else:
#             messagebox.showinfo("提示", "学号输入错误，请重新输入！")
# 

class LoginWindow:
    def __init__(self, root):
        self.root = root
        self.root.title('学生登录')
        self.root.geometry(f"{Width}x{Height}")
        self.root.resizable(False, False)
        self.root.wm_attributes('-topmost', True)
        #self.root.attributes('-disabled', True)  
        self.root.bind("<Configure>", lambda e: move_window(event=e,window=root))
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

        # Other GUI elements...

        self.username_label = ttk.Label(root, text='姓名:')
        self.username_label.grid(row=0, column=0, pady=5)
        self.username_entry = ttk.Entry(root)
        self.username_entry.grid(row=0, column=1, pady=5)

        self.password_label = ttk.Label(root, text='学号:')
        self.password_label.grid(row=1, column=0, pady=5)
        #self.password_entry = ttk.Entry(root, show='*')
        self.password_entry = ttk.Entry(root )
        self.password_entry.grid(row=1, column=1, pady=5)

        self.login_button = ttk.Button(root, text='登录', command=self.handle_login)
        self.login_button.grid(row=2, column=0, columnspan=2, pady=10)

        self.select_directory_button = ttk.Button(root, text='选择目录', command=self.open_directory_dialog, state=tk.DISABLED)
        self.select_directory_button.grid(row=3, column=0, columnspan=2, pady=10)

        self.path_label = ttk.Label(root, text="未选择目录")
        self.path_label.grid(row=4, column=0, columnspan=2, pady=5)

        self.text_display = tk.Text(root, height=10, width=50)
        self.text_display.grid(row=0, column=2, rowspan=6,padx=5,pady=5)
        self.text_display.config(state=tk.DISABLED)

    def open_directory_dialog(self):
        directory = filedialog.askdirectory(title="选择一个目录")
        if directory:
            if get_dir_size(directory) > 1024 * 1024 * 30:
                self.append_text("\n [重要信息]上传文件目录太大！！！")
                return False
            self.path_label.config(text=directory)
            now = datetime.datetime.now()
            formatted_now = now.strftime("%Y%m%d%H%M%S")
            filename = "A" + formatted_now + ".zip"
            with zipfile.ZipFile(filename, 'w', zipfile.ZIP_DEFLATED) as zipf:
                zipdir(directory, zipf)
            if not transfer_file(filename, clientip):
               MAIN_WIN.append_text("\n [文件传输失败]")
            else:
               MAIN_WIN.append_text("\n [文件传输成功]")
            #time.sleep(1)
            return True

    def handle_login(self):
        username = self.username_entry.get()
        password = self.password_entry.get()
        global student_no
        success, message = login(username, password)

        if success:
            self.login_button.config(state=tk.DISABLED)
            self.username_entry.config(state=tk.DISABLED)
            self.password_entry.config(state=tk.DISABLED)
            self.select_directory_button.config(state=tk.NORMAL)

            student_no = password
            thread = threading.Thread(target=run)
            threads.append(thread)
            thread_retrans = threading.Thread(target=retransfiles)
            threads.append(thread_retrans)
            thread_log = threading.Thread(target=send_log)
            threads.append(thread_log)
            thread.start()
            thread_retrans.start()
            thread_log.start()
            # messagebox.showinfo('登录信息', message)
            self.select_directory_button.config(state=tk.NORMAL)
            self.append_text("\n登录成功")
        else:
            messagebox.showinfo('登录信息', message)

    def append_text(self, message):
        self.text_display.config(state=tk.NORMAL)
        self.text_display.insert(tk.END, message)
        self.text_display.see(tk.END)
        self.text_display.config(state=tk.DISABLED)
    
    def on_closing(self):
        if messagebox.askokcancel("退出", "确定要退出吗?"):
            global program_run
            program_run = False
            for t in threads:                
                t.join()
                print(f"{t}结束")
            self.root.destroy()


def is_process_running(process_name):
    count = 0
    for process in psutil.process_iter():
        #logging.info(process.name())
        try:            
            if process_name.lower() in process.name().lower():
                count+=1                
                #logging.info(count)
        except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
            pass
    if count>2:
        return True                
    else:
        return False
    

def get_dir_size(dir_path):  
    size = 0  
    for root, dirs, files in os.walk(dir_path):  
        for f in files:  
            if os.path.isfile(os.path.join(root, f)):  
                size += os.path.getsize(os.path.join(root, f))  
    return size

def zipdir(path, ziph):  
    # ziph 是 zipfile.ZipFile 的一个实例  
    for root, dirs, files in os.walk(path):  
        for file in files:  
            ziph.write(os.path.join(root, file))
            
def move_window(event,window):  
    # 获取窗口的当前位置和大小
    x = window.winfo_x()  
    y = window.winfo_y()  
    width = window.winfo_width()  
    height = window.winfo_height()
    # 计算屏幕的范围  
    screen_width = window.winfo_screenwidth()  
    screen_height = window.winfo_screenheight()  
      
    # 计算窗口的新位置
    
    new_x = screen_width - width-10
#     if y==5:
#         new_y = screen_height - height - 10
#     else:
#         new_y = 5      
    new_y = 5
    # 移动窗口到新的位置  
    window.geometry("+{}+{}".format(new_x, new_y))



def main():
    # Other setup code...
    global MAIN_WIN
    process_name = os.path.basename(sys.argv[0])
    logging.info(process_name)

    if is_process_running(process_name):
        logging.info("程序已经在运行，不会启动第二个实例。")
    else:
        root = tk.Tk()
        window = LoginWindow(root)        
        MAIN_WIN = window
        root.mainloop()



if __name__ == "__main__":
    main()
