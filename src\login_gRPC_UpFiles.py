import grpc
from grpc_api import login_service_pb2  
from grpc_api import login_service_pb2_grpc
from grpc_api import file_transfer_pb2
from grpc_api import file_transfer_pb2_grpc

from PySide2.QtWidgets import QApplication, QWidget, QLineEdit, QPushButton, QVBoxLayout
from PySide2.QtWidgets import QFileDialog, QHBoxLayout, QLabel, QMessageBox, QDialog, QTextEdit
from PySide2.QtWidgets import QSizePolicy
from PySide2.QtGui import QPixmap, QImage,QTextCharFormat, QColor,QTextCursor
from PySide2.QtCore import Qt

import zipfile  
import socket
import os
import sys

import pyautogui
import io
import time
import json  
import cv2
import numpy as np
import datetime
from PIL import Image

import threading   
# import base64
# import login_image
import psutil
import logging

# 创建线程对象  
threads = []

# 主窗口
MAIN_WIN = None

# 读入config.json文件  
with open('config.json', 'r') as f:  
    config = json.load(f)  
  
# 提取服务器地址和端口参数  
server_ip = config.get('server_ip')  
server_port = config.get('server_port')
# 文件传输超时时间
file_trans_timeout = int(config.get('file_trans_timeout'))
# 文件传输失败后重试的次数
RE_MAX  = int(config.get('re_max'))
# 记录传输失败的文件队列
FAIL_FILES = dict()
# 定义保存视频的间隔时间（单位为秒）
video_save_interval = int(config.get('video_save_interval'))
# 截图间隔时间(单位：秒)
interval_time = int(config.get('interval_time'))
# 重传间隔时间
re_interval_time = int(config.get('re_interval_time'))
# windows 高
Height = int(config.get('height'))
# windows 宽
Width = int(config.get('width'))


SEND_MAX_SIZE = int(config.get('send_max_size'))
RECEIVE_MAX_SIZE = int(config.get('receive_max_size'))


# 配置日志记录  
logging.basicConfig(filename='log.txt', level=logging.DEBUG, format='%(asctime)s: %(message)s')

def is_server_up(ip, port):
    """检查服务器是否可达"""
    try:
        # 使用socket尝试连接服务器
        with socket.create_connection((ip, port), timeout=3):
            return True
    except (socket.timeout, ConnectionRefusedError, OSError):
        return False
    

def is_process_running(process_name):
    count = 0
    for process in psutil.process_iter():
        #logging.info(process.name())
        try:            
            if process_name.lower() in process.name().lower():
                count+=1                
                #logging.info(count)
        except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
            pass
    if count>2:
        return True                
    else:
        return False


student_no = '666'

hostname = socket.gethostname()
clientip = socket.gethostbyname(hostname)

program_run = True




# 获取屏幕尺寸
screen_width, screen_height = pyautogui.size()

# 设置视频编码器
fourcc = cv2.VideoWriter_fourcc(*"mp4v")


# 获取当前时间
start_time = time.time()
current_time = time.time()
end_time = current_time + video_save_interval


# 视频输出对象
out = None

def transfer_file(file_path, client_ip='localhost'):
    try:
        if is_server_up(server_ip,server_port):
            with open(file_path, 'rb') as f:  
                file_content = f.read()  
            global student_no
            channel = grpc.insecure_channel(f'{server_ip}:{server_port}',
                                            options=[('grpc.max_send_message_length',
                                                      SEND_MAX_SIZE),
                                                     ('grpc.max_receive_message_length',
                                                      RECEIVE_MAX_SIZE)])  
            stub = file_transfer_pb2_grpc.FileTransferServiceStub(channel)  
            response = stub.TransferFile(file_transfer_pb2.FileRequest(file_content=file_content,
                                                                       file_name=os.path.basename(file_path),
                                                                       client_ip=client_ip,
                                                                       student_id=student_no),timeout = file_trans_timeout
                                         )
            logging.info(f"文件{file_path}传输成功！")
            return response.success
        else:            
            logging.info(f"文件{file_path}传输失败！无法连接服务器。")
            if file_path in FAIL_FILES:
                FAIL_FILES[file_path] += 1
            else:
                FAIL_FILES[file_path] = 1
            return False
                
    except Exception as e:
        if file_path in FAIL_FILES:
                FAIL_FILES[file_path] += 1
        else:
                FAIL_FILES[file_path] = 1
        logging.info(e)
        return False
    
    
def send_log():
    global program_run
    Err = True
    while program_run:
        if transfer_file('log.txt',clientip) != True:            
           # MAIN_WIN.textEdit.append("\n [重要信息]服务器连接错误！！！", MAIN_WIN.format)
           cursor = QTextCursor(MAIN_WIN.textEdit.document())
           cursor.insertText("\n [重要信息]服务器连接错误！！！", MAIN_WIN.format)
           Err = True
        elif Err==True:
            MAIN_WIN.textEdit.append("\n [重要信息]服务器连接成功！！！")
            Err = False
        else:
            Err = False
        
        time.sleep(11)
        

def retransfiles():
    global program_run
    while program_run:
        for video_filename in list(FAIL_FILES):
            if FAIL_FILES[video_filename]<=RE_MAX:                
                if transfer_file(video_filename,clientip):
                    del FAIL_FILES[video_filename]
                else:
                    FAIL_FILES[video_filename]+=1
            else:
                logging.info(f"文件{video_filename}超过了最大重传数。")
        time.sleep(re_interval_time)
        


def run():
    #channel = grpc.insecure_channel(f'{server_ip}:{server_port}')
    #stub = screen_pb2_grpc.ScreenCaptureStub(channel)
    global program_run
    while program_run:
        # 获取当前时间
        global current_time
        global end_time
        global out
        current_time = time.time()

            # 截取屏幕图像
        try:
            screenshot = pyautogui.screenshot()
            resized_screenshot = screenshot.resize((1280, 720), Image.LANCZOS)
        except Exception as e:
            print(f"Error: {e}")

        screenshot_np = np.array(resized_screenshot)
        screenshot_cv = cv2.cvtColor(screenshot_np, cv2.COLOR_RGB2BGR)
        
        # 如果当前时间超过保存视频的时间间隔，创建新的视频文件
        if current_time >= end_time:
            if out is not None:
                out.release()  # 关闭之前的视频文件                
                # 将视频文件发送到服务器
                transfer_file(video_filename,clientip)

            # 获取当前时间的时间戳，并将其作为视频文件名
            # 获取当前日期和时间
            now = datetime.datetime.now()
            # 格式化日期和时间
            formatted_now = now.strftime("%Y%m%d%H%M%S")

            # 将格式化后的日期和时间字符串作为视频文件名
            video_filename = formatted_now + ".mp4"
            # video_filename = f"{int(current_time)}.mp4"
            # out = cv2.VideoWriter(video_filename, fourcc, 2.0, (screen_width, screen_height))
            out = cv2.VideoWriter(video_filename, fourcc, 2, (1280, 720))
            # 更新下一个视频保存时间
            end_time = current_time + video_save_interval

        # 保存截图到视频
        if out is not None:
            out.write(screenshot_cv)

        
        
        # screenshot = pyautogui.screenshot()
        # screenshot_bytes = io.BytesIO()

        # screenshot.save(screenshot_bytes, format="PNG")
        
    #    response = stub.SendScreen(screen_pb2.ScreenRequest(image=screenshot_bytes.getvalue()))
     #   print(response.status)
        
        # 暂停2秒
        time.sleep(interval_time)
    else:
        if out is not None:
                out.release()  # 关闭之前的视频文件                
                # 将视频文件发送到服务器
                transfer_file(video_filename,clientip)


class ConfirmExitDialog(QDialog):  
    def __init__(self, parent=None):  
        super().__init__(parent)  
        self.setWindowTitle('确认退出')  
          
        layout = QVBoxLayout()
        self.line_edit = QLineEdit(self)  
        layout.addWidget(self.line_edit)
        layout.addWidget(QLabel("输入你的学号退出! 退出后会关闭计算机!!!"))
          
        self.ok_button = QPushButton('确定', self)  
        self.ok_button.clicked.connect(self.accept)  
        layout.addWidget(self.ok_button)  
          
        self.setLayout(layout)  
      
    def text(self):  
        return self.line_edit.text()  


def login(username, password):
    try:
        print(f"{clientip}")
        with grpc.insecure_channel(f'{server_ip}:{server_port}') as channel:
#         channel = grpc.insecure_channel(f'{server_ip}:{server_port}')
            stub = login_service_pb2_grpc.LoginServiceStub(channel)
            request = login_service_pb2.LoginRequest(username=username, password=password,
                                                         clientip=clientip, hostname=hostname)
            if is_server_up(server_ip,server_port):
                response = stub.Login(request,timeout=3)
                return (response.success,response.message)
            else:
                return (False, "无法连接到服务器")               
            
        
#         return (False, "无法连接到服务器")
#     except grpc.aio.AioRpcError as e:  
#         print("Failed to connect to the server:", e)
    except grpc.RpcError as e:
        # 如果是连接失败的异常，打印消息并返回对应信息
        if e.code() == grpc.StatusCode.UNAVAILABLE:
            print("无法连接到服务器: 服务不可用")
        else:
            print(f"RPC调用失败: {e}")
        return (False, "无法连接到服务器")
    except BaseException as e:
         # 如果服务器连接失败，打印错误信息并返回  
        print(f"无法连接到服务器: {e}")  
        return (False, "无法连接到服务器")
    else:
        print("------")
    # return (False,"Not Success!")
# 示例：login('user', 'password')


def zipdir(path, ziph):  
    # ziph 是 zipfile.ZipFile 的一个实例  
    for root, dirs, files in os.walk(path):  
        for file in files:  
            ziph.write(os.path.join(root, file))  
            #ziph.write(file)  



class LoginWindow(QWidget):
    def __init__(self):        
        super(LoginWindow, self).__init__()        
        self.moving = False
        self.initUI()
        # 设置窗口的初始大小和位置  
        self.setGeometry(100, 100, Width, Height)  
        # 禁用最大化按钮  
        self.setWindowFlag(Qt.WindowMaximizeButtonHint, False)  
        # 禁用最小化按钮  
        self.setWindowFlag(Qt.WindowMinimizeButtonHint, False)  
        # 设置窗口大小固定，不能被用户更改
        self.adjustSize()
        
        
        self.setFixedSize(self.size())  

    def initUI(self):
        # 图片部分
        # self.imageLabel = QLabel(self)
        # image_data = base64.b64decode(login_image.base64_image)  
        # image = QImage()  
        # image.loadFromData(image_data)  
        # pixmap = QPixmap.fromImage(image)  
#         pixmap = QPixmap('ClassMonitorLoginSVG.jpg')
        # pixmap = pixmap.scaled(150, 250, Qt.KeepAspectRatio)
        # self.imageLabel.setPixmap(pixmap)
        # self.imageLabel.setAlignment(Qt.AlignCenter)
        
        

        # 登录表单部分
        self.username = QLineEdit(self)
        self.password = QLineEdit(self)
#         self.password.setEchoMode(QLineEdit.Password)
        #self.loginButton = QPushButton('登录', self)
        #self.loginButton.clicked.connect(self.handleLogin)
        #self.loginButton.clicked.connect(self.test)
        self.loginButton = QPushButton('登录', self)  
        self.loginButton.setStyleSheet("border-radius: 16px; background-color: red;")  # 设置为圆形按钮，默认红色背景  
        self.loginButton.clicked.connect(self.handleLogin)
        
        # 创建按钮并设置点击事件  
        self.btn = QPushButton("选择目录")  
        self.btn.clicked.connect(self.open_directory_dialog)
        # 设置按钮为不可用状态  
        self.btn.setEnabled(False)  

        formLayout = QVBoxLayout()
        formLayout.addWidget(QLabel('姓名:'))
        formLayout.addWidget(self.username)
        formLayout.addWidget(QLabel('学号:'))
        formLayout.addWidget(self.password)
        formLayout.addWidget(self.loginButton)
        formLayout.addWidget(self.btn)
        
        # 创建一个标签用于显示选择的目录路径  
        self.path_label = QLabel("未选择目录")  
        formLayout.addWidget(self.path_label)  


        # 主布局
        mainLayout = QHBoxLayout()
        self.textEdit = QTextEdit()
        self.textEdit.setFixedWidth(self.textEdit.fontMetrics().horizontalAdvance('01234567890123456789'))  # 设置宽度为20字符  
        self.textEdit.setSizePolicy(QSizePolicy.Fixed, QSizePolicy.Expanding)  # 设置高度可扩展
        self.textEdit.setReadOnly(True)  # 设置为只读模式 
        self.textEdit.append("输入姓名学号登录")
        # 创建并配置字符格式  
        self.format = QTextCharFormat()  
        self.format.setForeground(QColor("red"))  # 设置颜色为红色  
  
        # 使用 QTextCursor 插入带格式的文本  
        
        mainLayout.addWidget(self.textEdit)          
        # mainLayout.addWidget(self.imageLabel)
        mainLayout.addLayout(formLayout)

        self.setLayout(mainLayout)
        self.setWindowTitle('学生登录')
    
    def open_directory_dialog(self):  
        # 打开目录选择对话框  
        directory = QFileDialog.getExistingDirectory(self, "选择一个目录")  
        if directory:
            if get_dir_size(directory)>1024*1024*1:
                cursor = QTextCursor(self.textEdit.document())
                cursor.insertText("\n [重要信息]上传文件目录太大！！！", self.format)           
                return False
            self.path_label.setText(directory)
#            files = [f for f in os.listdir(directory) if os.path.isfile(f)]
            # files = [f for f in os.listdir(directory)]
            # print(directory)            
            #print(files)
           # 获取当前日期和时间
            now = datetime.datetime.now()
            # 格式化日期和时间
            formatted_now = now.strftime("%Y%m%d%H%M%S")

            # 将格式化后的日期和时间字符串作为视频文件名
            filename = "A"+formatted_now + ".zip"

            
            zipf = zipfile.ZipFile(filename, 'w', zipfile.ZIP_DEFLATED)  
            zipdir(directory, zipf)  
            zipf.close()            
            transfer_file(filename,clientip)            
            time.sleep(1)
            return True
            
        
    def moveEvent(self, event):
        if self.moving:
            return

        self.moving = True

        screen = QApplication.primaryScreen().geometry()
        window = self.geometry()

        new_x = window.x()
        new_y = window.y()

        if window.left() < 0:
            new_x = 0
        if window.right() > screen.right():
            new_x = screen.right() - window.width()
        if window.top() < 0:
            new_y = 0
        if window.bottom() > screen.bottom():
            new_y = screen.bottom() - window.height()

        self.move(new_x, new_y)
        self.moving = False


    def handleLogin(self):
        username = self.username.text()
        password = self.password.text()
        global student_no
        success, message = login(username, password)
         # 根据登录成功或失败更改按钮颜色  
        if success:  
            self.loginButton.setStyleSheet("border-radius: 16px; background-color: green;")  # 登录成功，设置为绿色背景
            self.loginButton.setDisabled(True)  # 登录成功后禁用登录按钮
            self.username.setReadOnly(True)
            self.password.setReadOnly(True)
            student_no = password
            thread = threading.Thread(target=run)
            threads.append(thread)
            thread_retrans = threading.Thread(target=retransfiles)
            threads.append(thread_retrans)
            thread_log = threading.Thread(target=send_log)
            threads.append(thread_log)
            thread.start()
            thread_retrans.start()
            thread_log.start()
            QMessageBox.information(self, '登录信息', message, QMessageBox.Ok)
            self.btn.setEnabled(True)
            self.textEdit.append("\n登录成功")
        else:  
            self.loginButton.setStyleSheet("border-radius: 16px; background-color: red;")  # 登录失败，设置为红色背景
            QMessageBox.information(self, '登录信息', message, QMessageBox.Ok)
          
        # login(username, password)
        # print(f"{success} message:{message}")
    
        
        # QMessageBox.information(self, '登录信息', "OK", QMessageBox.Ok)
        
    def closeEvent(self, event):
        global program_run
        dialog = ConfirmExitDialog(self)  
        if dialog.exec_() == QDialog.Accepted and dialog.text().lower() == student_no:            
            program_run = False
            for t in threads:  
                t.join()  
            os.system('shutdown /s /t 0')  # 适用于Windows系统
            event.accept()  
        else:  
            event.ignore()

def get_dir_size(dir_path):  
    size = 0  
    for root, dirs, files in os.walk(dir_path):  
        for f in files:  
            if os.path.isfile(os.path.join(root, f)):  
                size += os.path.getsize(os.path.join(root, f))  
    return size  

    
process_name = os.path.basename(sys.argv[0])
logging.info(process_name)


if is_process_running(process_name):
    logging.info("程序已经在运行，不会启动第二个实例。")
else:
    app = QApplication([])
    window = LoginWindow()
    MAIN_WIN = window
    # 设置窗口总在最前面  
    window.setWindowFlags(window.windowFlags() | Qt.WindowStaysOnTopHint)
    window.show()
    app.exec_()
